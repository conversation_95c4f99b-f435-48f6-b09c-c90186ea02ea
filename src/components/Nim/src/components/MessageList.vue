<!--
 * @description 消息列表
-->

<script setup lang="ts">
import MessageProvider from "./MessageProvider.vue";
import MessageBubble from "./MessageBubble.vue";
import { Message } from "../types/message";
import { NimEvent } from "../types/nim";
import { useNim } from "../hooks/useNim";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  messageList: Message[];
}>();

const nim = useNim();
const elScrollbarRef = ref();

// 消息列表内容器元素
const messageListInnerRef = ref();

// 防抖定时器
let scrollTimer: ReturnType<typeof setTimeout> | null = null;

// 实际执行滚动的函数
const doScroll = () => {
  if (elScrollbarRef.value) {
    const scrollHeight = elScrollbarRef.value.scrollHeight;
    const clientHeight = elScrollbarRef.value.clientHeight;
    const maxScrollTop = Math.max(0, scrollHeight - clientHeight);

    console.log(
      `滚动信息: scrollHeight=${scrollHeight}, clientHeight=${clientHeight}, maxScrollTop=${maxScrollTop}`
    );

    // 直接设置到最大滚动位置，避免多次设置造成抖动
    elScrollbarRef.value.scrollTop = maxScrollTop;
    console.log(`设置scrollTop为: ${maxScrollTop}`);
  } else {
    console.log("elScrollbarRef.value 为空，无法滚动");
  }
};

// 防抖滚动到底部函数
const scrollToBottom = () => {
  console.log("开始执行滚动到底部");

  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  // 设置新的定时器
  scrollTimer = setTimeout(() => {
    nextTick(() => {
      doScroll();
    });
  }, 100); // 100ms防抖延迟，减少频繁调用
};

// 处理接受到消息
const onMsgHandler = (message: Message) => {
  console.log("收到新消息，准备滚动到底部:", message);
  // 接收到新消息时，使用防抖滚动
  scrollToBottom();
};

// 处理发送消息
const onSendMsgHandler = () => {
  console.log("发送消息，准备滚动到底部");
  // 发送消息时，使用防抖滚动
  scrollToBottom();
};

// 监听接收到消息
nim.on(NimEvent.MSG, onMsgHandler);
// 监听发送消息
nim.on(NimEvent.SEND_MSG, onSendMsgHandler);

// 监听消息列表变化，当有新消息时自动滚动到底部
watch(
  () => props.messageList.length,
  (newLength, oldLength) => {
    console.log(`消息列表长度变化: ${oldLength} -> ${newLength}`);
    // 当消息列表长度变化时，使用防抖滚动
    if (newLength > (oldLength || 0)) {
      console.log("检测到新消息，执行滚动");
      scrollToBottom();
    }
  }
);

onMounted(scrollToBottom);

onUnmounted(() => {
  // 清理定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  nim.off(NimEvent.MSG, onMsgHandler);
  nim.off(NimEvent.SEND_MSG, onSendMsgHandler);
});

// const messageHeight = ref('442px')
</script>

<template>
  <div ref="elScrollbarRef" class="message-list">
    <div ref="messageListInnerRef" class="message-list-inner">
      <MessageProvider
        v-for="(message, index) in props.messageList"
        :key="message.idClient"
        :message="message"
        :prev-message="props.messageList[index - 1]"
      >
        <MessageBubble />
      </MessageProvider>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }

  .message-list-inner {
    padding: 6px 0;
  }
}
</style>
