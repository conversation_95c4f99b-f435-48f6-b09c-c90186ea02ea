(function (window) {
  window.RcpWeb = {
    RemoteUrl: "http://192.168.10.204:7017/",
    WebSocketUrl: "wss://local.sqray.com:8811", //ws://192.168.10.50:8866  //wss://dev.sqray.com:8880 //wss://rcp.sqray.com:8880 //wss://h3cdev.sqray.com:8877
    WebSocketProxyUrl: "wss://local.sqray.com:8811",
    decodeSdpcCoreJsPath: "http://192.168.20.44:8889/hevc.js", //切片解码库地址
    decodeSdpcCoreWasmPath: "http://192.168.20.44:8889/hevc.wasm", //切片解码库地址
    sharePath: "http://192.168.10.50/mobile/", //分享地址
    Bucket: 'test:',
    HomeTitle: '省级病理会诊平台',
    BackEndUrl: "http://192.168.10.204:7013",//后端解码接口地址
    IsBackEnd: true,//是否后端解码
    readingStore: 'ServerDiskFile',//后台切片获取需要的地址,
    plugStorageUploaUrl: 'http://127.0.0.1:57013', //上传插件存储接口地址
    IsEvaluate: true,//是否开启切片评价
  };
})(window);
